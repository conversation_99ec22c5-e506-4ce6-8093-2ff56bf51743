import React, { useState, useEffect, useContext } from 'react';
import { Button, List, Modal, Input, Select, InputNumber, Space, Card, Typography, Divider, message, Progress } from 'antd';
import { PlayCircleOutlined, StopOutlined, DeleteOutlined, EditOutlined, PlusOutlined, SettingOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useHandleDevice } from '../HIDDevice/HandleDeviceContext';
import { KeyboardContext } from '../Keyboard/KeyboardContext';
import { useMacro } from './MacroContext';
import useMacroRecording from './useMacroRecording';
import TimingSettings from './TimingSettings';
import './MacroRecording.css';

const { Title, Text } = Typography;
const { Option } = Select;

const MacroRecording = () => {
  const { t } = useTranslation();
  const { addToQueue } = useHandleDevice();
  const { data } = useContext(KeyboardContext);

  // 使用宏上下文
  const {
    macros,
    macroConfig,
    addMacro,
    updateMacro,
    deleteMacro,
    getMacroStats,
    syncWithDevice
  } = useMacro();

  // 使用录制钩子
  const {
    isRecording,
    recordedEvents,
    startRecording: startRecordingHook,
    stopRecording: stopRecordingHook,
    resetRecording,
    getRecordingStats,
    validateRecording
  } = useMacroRecording();

  // 时间设置
  const [timingMode, setTimingMode] = useState('actual');
  const [customInterval, setCustomInterval] = useState(50);
  const [showAdvancedTiming, setShowAdvancedTiming] = useState(false);

  // 模态框状态
  const [showNameModal, setShowNameModal] = useState(false);
  const [macroName, setMacroName] = useState('');
  const [editingMacro, setEditingMacro] = useState(null);

  useEffect(() => {
    // 初始化时同步设备配置
    syncWithDevice();
  }, [syncWithDevice]);

  // 开始录制
  const startRecording = () => {
    if (macros.length >= macroConfig.maxMacroCount) {
      message.error(t('macro_recording.max_macros_reached'));
      return;
    }

    startRecordingHook();
  };

  // 停止录制
  const stopRecording = () => {
    const events = stopRecordingHook();

    if (events && events.length > 0) {
      const validation = validateRecording(events);
      if (!validation.valid) {
        message.error(validation.error);
        return;
      }

      setMacroName(`${t('macro_recording.new_macro')} ${macros.length + 1}`);
      setShowNameModal(true);
    } else {
      message.warning('No events recorded');
    }
  };

  // 保存宏
  const saveMacro = () => {
    if (!macroName.trim()) {
      message.error('Please enter a macro name');
      return;
    }

    const macroData = {
      name: macroName,
      events: recordedEvents,
      timingMode,
      customInterval
    };

    if (editingMacro) {
      updateMacro(editingMacro.id, macroData);
      setEditingMacro(null);
      message.success(t('macro_recording.rename_macro'));
    } else {
      addMacro(macroData);
      message.success(t('macro_recording.save'));
    }

    setShowNameModal(false);
    setMacroName('');
    resetRecording();
  };

  // 播放宏
  const playMacro = (macro) => {
    message.info(`${t('macro_recording.play_macro')}: ${macro.name}`);
    // 这里可以添加播放逻辑
  };

  // 删除宏
  const handleDeleteMacro = (macroId) => {
    Modal.confirm({
      title: t('macro_recording.confirm_delete'),
      onOk: () => {
        deleteMacro(macroId);
        message.success(t('macro_recording.delete_macro'));
      }
    });
  };

  // 重命名宏
  const renameMacro = (macro) => {
    setEditingMacro(macro);
    setMacroName(macro.name);
    setShowNameModal(true);
  };

  // 获取统计信息
  const stats = getMacroStats();
  const recordingStats = getRecordingStats();

  return (
    <div className="macro-recording-container">
      <Card>
        <Title level={3}>{t('macro_recording.title')}</Title>

        {/* 统计信息 */}
        <div className="macro-stats">
          <div className="macro-stat-item">
            <span className="macro-stat-value">{stats.totalMacros}</span>
            <span className="macro-stat-label">总宏数</span>
          </div>
          <div className="macro-stat-item">
            <span className="macro-stat-value">{stats.maxMacros}</span>
            <span className="macro-stat-label">最大数量</span>
          </div>
          <div className="macro-stat-item">
            <span className="macro-stat-value">{stats.bufferUsage}%</span>
            <span className="macro-stat-label">缓存使用</span>
          </div>
          <div className="macro-stat-item">
            <span className="macro-stat-value">{recordingStats.totalEvents}</span>
            <span className="macro-stat-label">当前事件</span>
          </div>
        </div>

        {/* 录制控制区域 */}
        <div className="recording-controls">
          <Space size="large" style={{ width: '100%', justifyContent: 'space-between' }}>
            <div>
              <Text strong>{t('macro_recording.recording_status')}: </Text>
              <Text type={isRecording ? "warning" : "secondary"}>
                {isRecording ? t('macro_recording.recording') : t('macro_recording.stopped')}
              </Text>
              {isRecording && <div className="recording-indicator" />}
            </div>

            <Space>
              {!isRecording ? (
                <Button
                  type="primary"
                  icon={<PlayCircleOutlined />}
                  onClick={startRecording}
                  disabled={macros.length >= macroConfig.maxMacroCount}
                >
                  {t('macro_recording.start_recording')}
                </Button>
              ) : (
                <Button
                  danger
                  icon={<StopOutlined />}
                  onClick={stopRecording}
                >
                  {t('macro_recording.stop_recording')}
                </Button>
              )}

              <Button
                icon={<SettingOutlined />}
                onClick={() => setShowAdvancedTiming(!showAdvancedTiming)}
              >
                时间设置
              </Button>
            </Space>
          </Space>
        </div>

        <Divider />

        {/* 时间设置区域 */}
        {showAdvancedTiming && (
          <TimingSettings
            timingMode={timingMode}
            onTimingModeChange={setTimingMode}
            customInterval={customInterval}
            onCustomIntervalChange={setCustomInterval}
            disabled={isRecording}
            showAdvanced={true}
          />
        )}

        {/* 录制事件预览 */}
        {isRecording && recordedEvents.length > 0 && (
          <div style={{ marginTop: 16 }}>
            <Title level={5}>录制预览</Title>
            <div className="macro-events-preview">
              {recordedEvents.slice(-10).map((event, index) => (
                <div key={index} className="macro-event-item">
                  <span className={`macro-event-type ${event.type}`}>
                    {event.type === 'keydown' ? '↓' : '↑'}
                  </span>
                  <span className="macro-event-key">{event.key}</span>
                  <span className="macro-event-timestamp">
                    {event.relativeTime}ms
                  </span>
                </div>
              ))}
              {recordedEvents.length > 10 && (
                <div className="macro-event-item">
                  <Text type="secondary">... 还有 {recordedEvents.length - 10} 个事件</Text>
                </div>
              )}
            </div>
          </div>
        )}

        <Divider />

        {/* 宏列表 */}
        <div className="macro-list">
          <Title level={4}>{t('macro_recording.macro_list')}</Title>
          {macros.length === 0 ? (
            <Text type="secondary">{t('macro_recording.no_macros')}</Text>
          ) : (
            <List
              dataSource={macros}
              renderItem={(macro) => (
                <List.Item
                  actions={[
                    <Button
                      icon={<PlayCircleOutlined />}
                      onClick={() => playMacro(macro)}
                      size="small"
                    >
                      {t('macro_recording.play_macro')}
                    </Button>,
                    <Button
                      icon={<EditOutlined />}
                      onClick={() => renameMacro(macro)}
                      size="small"
                    >
                      {t('macro_recording.rename_macro')}
                    </Button>,
                    <Button
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => handleDeleteMacro(macro.id)}
                      size="small"
                    >
                      {t('macro_recording.delete_macro')}
                    </Button>
                  ]}
                >
                  <List.Item.Meta
                    title={macro.name}
                    description={`${macro.events.length} events • ${macro.timingMode} timing`}
                  />
                </List.Item>
              )}
            />
          )}
        </div>
      </Card>

      {/* 命名模态框 */}
      <Modal
        title={editingMacro ? t('macro_recording.rename_macro') : t('macro_recording.new_macro')}
        open={showNameModal}
        onOk={saveMacro}
        onCancel={() => {
          setShowNameModal(false);
          setMacroName('');
          setEditingMacro(null);
          if (!editingMacro) {
            resetRecording();
          }
        }}
        okText={t('macro_recording.save')}
        cancelText={t('macro_recording.cancel')}
      >
        <Input
          value={macroName}
          onChange={(e) => setMacroName(e.target.value)}
          placeholder={t('macro_recording.macro_name')}
          autoFocus
        />
      </Modal>
    </div>
  );
};

export default MacroRecording;
