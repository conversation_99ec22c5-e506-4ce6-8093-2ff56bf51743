import { useState, useEffect, useCallback, useRef } from 'react';
import { message } from 'antd';

export const useMacroRecording = () => {
  const [isRecording, setIsRecording] = useState(false);
  const [recordedEvents, setRecordedEvents] = useState([]);
  const [startTime, setStartTime] = useState(null);
  const recordingRef = useRef(false);
  const eventsRef = useRef([]);

  // 重置录制状态
  const resetRecording = useCallback(() => {
    setRecordedEvents([]);
    setStartTime(null);
    eventsRef.current = [];
  }, []);

  // 开始录制
  const startRecording = useCallback(() => {
    if (recordingRef.current) return;
    
    recordingRef.current = true;
    setIsRecording(true);
    setStartTime(Date.now());
    resetRecording();
    
    message.info('开始录制宏键，按下任意键开始录制...');
  }, [resetRecording]);

  // 停止录制
  const stopRecording = useCallback(() => {
    if (!recordingRef.current) return;
    
    recordingRef.current = false;
    setIsRecording(false);
    
    const finalEvents = [...eventsRef.current];
    setRecordedEvents(finalEvents);
    
    message.success(`录制完成，共录制 ${finalEvents.length} 个事件`);
    
    return finalEvents;
  }, []);

  // 处理键盘事件
  const handleKeyboardEvent = useCallback((event, eventType) => {
    if (!recordingRef.current) return;
    
    // 防止录制系统按键和修饰键组合
    if (event.ctrlKey || event.altKey || event.metaKey) {
      return;
    }
    
    // 防止录制特殊按键
    const excludedKeys = ['F5', 'F12', 'Tab', 'Alt', 'Control', 'Meta', 'Shift'];
    if (excludedKeys.includes(event.key)) {
      return;
    }
    
    const currentTime = Date.now();
    const relativeTime = startTime ? currentTime - startTime : 0;
    
    const eventData = {
      id: `${eventType}-${currentTime}-${Math.random()}`,
      type: eventType,
      key: event.key,
      keyCode: event.keyCode,
      code: event.code,
      timestamp: currentTime,
      relativeTime,
      shiftKey: event.shiftKey,
      ctrlKey: event.ctrlKey,
      altKey: event.altKey,
      metaKey: event.metaKey
    };
    
    eventsRef.current.push(eventData);
    setRecordedEvents(prev => [...prev, eventData]);
  }, [startTime]);

  // 键盘按下事件处理器
  const handleKeyDown = useCallback((event) => {
    handleKeyboardEvent(event, 'keydown');
  }, [handleKeyboardEvent]);

  // 键盘释放事件处理器
  const handleKeyUp = useCallback((event) => {
    handleKeyboardEvent(event, 'keyup');
  }, [handleKeyboardEvent]);

  // 添加/移除事件监听器
  useEffect(() => {
    if (isRecording) {
      document.addEventListener('keydown', handleKeyDown, true);
      document.addEventListener('keyup', handleKeyUp, true);
    } else {
      document.removeEventListener('keydown', handleKeyDown, true);
      document.removeEventListener('keyup', handleKeyUp, true);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown, true);
      document.removeEventListener('keyup', handleKeyUp, true);
    };
  }, [isRecording, handleKeyDown, handleKeyUp]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      recordingRef.current = false;
      document.removeEventListener('keydown', handleKeyDown, true);
      document.removeEventListener('keyup', handleKeyUp, true);
    };
  }, [handleKeyDown, handleKeyUp]);

  // 计算录制统计信息
  const getRecordingStats = useCallback(() => {
    const keyDownEvents = recordedEvents.filter(e => e.type === 'keydown');
    const keyUpEvents = recordedEvents.filter(e => e.type === 'keyup');
    const totalDuration = recordedEvents.length > 0 
      ? recordedEvents[recordedEvents.length - 1].relativeTime 
      : 0;
    
    const uniqueKeys = new Set(recordedEvents.map(e => e.key));
    
    return {
      totalEvents: recordedEvents.length,
      keyDownEvents: keyDownEvents.length,
      keyUpEvents: keyUpEvents.length,
      uniqueKeys: uniqueKeys.size,
      duration: totalDuration,
      averageInterval: recordedEvents.length > 1 
        ? totalDuration / (recordedEvents.length - 1) 
        : 0
    };
  }, [recordedEvents]);

  // 转换为协议格式
  const convertToProtocolFormat = useCallback((events, timingMode = 'actual', customInterval = 50) => {
    const protocolData = [];
    
    for (let i = 0; i < events.length; i++) {
      const event = events[i];
      
      // 添加按键事件
      if (event.type === 'keydown') {
        protocolData.push(0x01); // 按下
        protocolData.push(event.keyCode & 0xFF);
      } else if (event.type === 'keyup') {
        protocolData.push(0x02); // 释放
        protocolData.push(event.keyCode & 0xFF);
      }
      
      // 添加时间间隔（除了最后一个事件）
      if (i < events.length - 1) {
        let interval;
        
        switch (timingMode) {
          case 'actual':
            interval = events[i + 1].relativeTime - event.relativeTime;
            break;
          case 'standard':
            interval = 50; // 标准50ms
            break;
          case 'custom':
            interval = customInterval;
            break;
          default:
            interval = 50;
        }
        
        // 限制间隔时间在合理范围内
        interval = Math.max(1, Math.min(interval, 255));
        
        protocolData.push(0x03); // 延时
        protocolData.push(interval);
      }
    }
    
    return protocolData;
  }, []);

  // 验证录制数据
  const validateRecording = useCallback((events) => {
    if (!events || events.length === 0) {
      return { valid: false, error: '没有录制到任何事件' };
    }
    
    if (events.length > 1000) {
      return { valid: false, error: '录制事件过多，请缩短录制时间' };
    }
    
    // 检查是否有配对的按键事件
    const keyDownCount = events.filter(e => e.type === 'keydown').length;
    const keyUpCount = events.filter(e => e.type === 'keyup').length;
    
    if (Math.abs(keyDownCount - keyUpCount) > 5) {
      return { 
        valid: false, 
        error: '按键按下和释放事件不匹配，可能录制不完整' 
      };
    }
    
    return { valid: true };
  }, []);

  return {
    isRecording,
    recordedEvents,
    startRecording,
    stopRecording,
    resetRecording,
    getRecordingStats,
    convertToProtocolFormat,
    validateRecording
  };
};

export default useMacroRecording;
