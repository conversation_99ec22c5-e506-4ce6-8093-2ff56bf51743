// 设置宏键配置的协议处理

const SetMacro = (hexArray, setMacroConfig) => {
  if (hexArray[0] === '0C') {
    // 获取键盘总的最大可设置macro数目
    const maxMacroCount = parseInt(hexArray[1], 16);
    setMacroConfig(prev => ({
      ...prev,
      maxMacroCount
    }));
  } else if (hexArray[0] === '0D') {
    // 获取键盘macro缓存大小
    const bufferSize = parseInt(hexArray[1] + hexArray[2], 16);
    setMacroConfig(prev => ({
      ...prev,
      bufferSize
    }));
  } else if (hexArray[0] === '0E') {
    // 获取键盘macro配置数据
    const offsetHigh = parseInt(hexArray[1], 16);
    const offsetLow = parseInt(hexArray[2], 16);
    const dataLength = parseInt(hexArray[3], 16);
    const offset = (offsetHigh << 8) | offsetLow;

    // 提取数据部分
    const data = hexArray.slice(4, 4 + dataLength);

    setMacroConfig(prev => ({
      ...prev,
      configData: {
        ...prev.configData,
        [offset]: data
      }
    }));
  }
};

export default SetMacro;
