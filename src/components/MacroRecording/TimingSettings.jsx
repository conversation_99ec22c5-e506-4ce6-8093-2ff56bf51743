import React, { useState, useEffect } from 'react';
import { Card, Select, InputNumber, Space, Typography, Slider, Switch, Divider, Alert } from 'antd';
import { ClockCircleOutlined, SettingOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

const TimingSettings = ({ 
  timingMode, 
  onTimingModeChange, 
  customInterval, 
  onCustomIntervalChange,
  disabled = false,
  showAdvanced = false 
}) => {
  const { t } = useTranslation();
  const [advancedSettings, setAdvancedSettings] = useState({
    useVariableDelay: false,
    minDelay: 10,
    maxDelay: 100,
    delayVariation: 20,
    humanizeTimings: false
  });

  // 预设的时间间隔选项
  const presetIntervals = [
    { value: 10, label: '10ms - 极快' },
    { value: 25, label: '25ms - 很快' },
    { value: 50, label: '50ms - 标准' },
    { value: 100, label: '100ms - 慢速' },
    { value: 200, label: '200ms - 很慢' }
  ];

  // 时间模式描述
  const getTimingModeDescription = (mode) => {
    switch (mode) {
      case 'actual':
        return '使用录制时的实际按键间隔时间，最真实地重现操作';
      case 'standard':
        return '使用固定的50ms间隔，适合大多数应用场景';
      case 'custom':
        return '使用自定义间隔时间，可以根据需要调整速度';
      default:
        return '';
    }
  };

  // 计算建议的间隔时间
  const getSuggestedInterval = (events) => {
    if (!events || events.length < 2) return 50;
    
    const intervals = [];
    for (let i = 1; i < events.length; i++) {
      const interval = events[i].relativeTime - events[i - 1].relativeTime;
      if (interval > 0 && interval < 1000) {
        intervals.push(interval);
      }
    }
    
    if (intervals.length === 0) return 50;
    
    // 计算平均间隔
    const avgInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
    return Math.round(avgInterval);
  };

  // 人性化时间处理
  const humanizeInterval = (baseInterval) => {
    if (!advancedSettings.humanizeTimings) return baseInterval;
    
    const variation = advancedSettings.delayVariation;
    const randomFactor = (Math.random() - 0.5) * 2 * (variation / 100);
    return Math.max(1, Math.round(baseInterval * (1 + randomFactor)));
  };

  // 处理高级设置变化
  const handleAdvancedSettingChange = (key, value) => {
    setAdvancedSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  return (
    <Card 
      title={
        <Space>
          <ClockCircleOutlined />
          <span>{t('macro_recording.timing_mode')}</span>
        </Space>
      }
      size="small"
    >
      {/* 基础时间模式选择 */}
      <div style={{ marginBottom: 16 }}>
        <Text strong>时间模式：</Text>
        <Select
          value={timingMode}
          onChange={onTimingModeChange}
          style={{ width: '100%', marginTop: 8 }}
          disabled={disabled}
        >
          <Option value="actual">
            <Space>
              <ClockCircleOutlined />
              {t('macro_recording.actual_timing')}
            </Space>
          </Option>
          <Option value="standard">
            <Space>
              <SettingOutlined />
              {t('macro_recording.standard_timing')}
            </Space>
          </Option>
          <Option value="custom">
            <Space>
              <SettingOutlined />
              {t('macro_recording.custom_timing')}
            </Space>
          </Option>
        </Select>
        
        <Alert
          message={getTimingModeDescription(timingMode)}
          type="info"
          showIcon
          style={{ marginTop: 8, fontSize: 12 }}
        />
      </div>

      {/* 自定义间隔设置 */}
      {timingMode === 'custom' && (
        <div style={{ marginBottom: 16 }}>
          <Text strong>{t('macro_recording.interval_ms')}：</Text>
          
          {/* 预设选项 */}
          <div style={{ marginTop: 8, marginBottom: 12 }}>
            <Text type="secondary" style={{ fontSize: 12 }}>快速选择：</Text>
            <div style={{ marginTop: 4 }}>
              {presetIntervals.map(preset => (
                <button
                  key={preset.value}
                  onClick={() => onCustomIntervalChange(preset.value)}
                  disabled={disabled}
                  style={{
                    margin: '2px 4px 2px 0',
                    padding: '4px 8px',
                    border: customInterval === preset.value ? '2px solid #1890ff' : '1px solid #d9d9d9',
                    borderRadius: 4,
                    background: customInterval === preset.value ? '#e6f7ff' : '#fff',
                    cursor: disabled ? 'not-allowed' : 'pointer',
                    fontSize: 11
                  }}
                >
                  {preset.label}
                </button>
              ))}
            </div>
          </div>

          {/* 滑块调节 */}
          <div style={{ marginBottom: 12 }}>
            <Slider
              min={1}
              max={500}
              value={customInterval}
              onChange={onCustomIntervalChange}
              disabled={disabled}
              marks={{
                1: '1ms',
                50: '50ms',
                100: '100ms',
                200: '200ms',
                500: '500ms'
              }}
              tooltip={{
                formatter: (value) => `${value}ms`
              }}
            />
          </div>

          {/* 精确数值输入 */}
          <InputNumber
            value={customInterval}
            onChange={onCustomIntervalChange}
            min={1}
            max={1000}
            addonAfter="ms"
            disabled={disabled}
            style={{ width: '100%' }}
            placeholder={t('macro_recording.interval_ms')}
          />
        </div>
      )}

      {/* 高级设置 */}
      {showAdvanced && (
        <>
          <Divider orientation="left" style={{ fontSize: 12 }}>
            高级设置
          </Divider>
          
          <div style={{ marginBottom: 12 }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
              <Text>可变延时</Text>
              <Switch
                checked={advancedSettings.useVariableDelay}
                onChange={(checked) => handleAdvancedSettingChange('useVariableDelay', checked)}
                disabled={disabled}
                size="small"
              />
            </div>
            <Text type="secondary" style={{ fontSize: 11 }}>
              在设定间隔基础上添加随机变化，使宏更自然
            </Text>
          </div>

          {advancedSettings.useVariableDelay && (
            <div style={{ marginBottom: 12, paddingLeft: 16 }}>
              <div style={{ marginBottom: 8 }}>
                <Text style={{ fontSize: 12 }}>变化幅度：{advancedSettings.delayVariation}%</Text>
                <Slider
                  min={0}
                  max={50}
                  value={advancedSettings.delayVariation}
                  onChange={(value) => handleAdvancedSettingChange('delayVariation', value)}
                  disabled={disabled}
                  size="small"
                />
              </div>
              
              <div style={{ display: 'flex', gap: 8 }}>
                <div style={{ flex: 1 }}>
                  <Text style={{ fontSize: 11 }}>最小延时：</Text>
                  <InputNumber
                    size="small"
                    min={1}
                    max={advancedSettings.maxDelay - 1}
                    value={advancedSettings.minDelay}
                    onChange={(value) => handleAdvancedSettingChange('minDelay', value)}
                    disabled={disabled}
                    addonAfter="ms"
                    style={{ width: '100%' }}
                  />
                </div>
                <div style={{ flex: 1 }}>
                  <Text style={{ fontSize: 11 }}>最大延时：</Text>
                  <InputNumber
                    size="small"
                    min={advancedSettings.minDelay + 1}
                    max={1000}
                    value={advancedSettings.maxDelay}
                    onChange={(value) => handleAdvancedSettingChange('maxDelay', value)}
                    disabled={disabled}
                    addonAfter="ms"
                    style={{ width: '100%' }}
                  />
                </div>
              </div>
            </div>
          )}

          <div style={{ marginBottom: 12 }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
              <Text>人性化时间</Text>
              <Switch
                checked={advancedSettings.humanizeTimings}
                onChange={(checked) => handleAdvancedSettingChange('humanizeTimings', checked)}
                disabled={disabled}
                size="small"
              />
            </div>
            <Text type="secondary" style={{ fontSize: 11 }}>
              模拟人类操作的不规律性，避免被检测为机器操作
            </Text>
          </div>
        </>
      )}

      {/* 时间预览 */}
      {timingMode === 'custom' && (
        <div style={{ 
          marginTop: 16, 
          padding: 8, 
          background: '#f5f5f5', 
          borderRadius: 4,
          fontSize: 11
        }}>
          <Text type="secondary">
            预览：每个按键间隔 {customInterval}ms
            {advancedSettings.useVariableDelay && 
              ` (±${advancedSettings.delayVariation}%)`
            }
          </Text>
        </div>
      )}
    </Card>
  );
};

export default TimingSettings;
