# 宏键录制功能 (Macro Recording)

## 功能概述

宏键录制功能允许用户录制键盘操作序列并将其保存为宏，支持多种时间间隔模式和高级设置。

## 主要特性

### 1. 宏键录制
- 实时键盘事件捕获
- 支持按键按下和释放事件
- 自动记录时间间隔
- 录制状态可视化指示

### 2. 时间间隔设置
- **实际时间模式**: 使用录制时的真实时间间隔
- **标准间隔模式**: 使用固定50ms间隔
- **自定义间隔模式**: 用户自定义时间间隔
- 支持1-1000ms范围调节
- 预设快速选择选项

### 3. 高级时间设置
- 可变延时: 添加随机时间变化
- 人性化时间: 模拟人类操作不规律性
- 最小/最大延时限制
- 变化幅度百分比控制

### 4. 宏管理
- 宏列表显示和管理
- 宏重命名功能
- 宏删除确认
- 宏播放功能
- 统计信息显示

### 5. 协议通信
- 支持VIA标准协议
- 获取设备宏配置信息
- 设置宏数据到设备
- 重置宏配置

## 协议规范

### VIA协议命令

#### 获取命令
- `0x0C`: 获取最大宏数量
- `0x0D`: 获取宏缓存大小  
- `0x0E`: 获取宏配置数据

#### 设置命令
- `0x0F`: 设置宏配置数据
- `0x10`: 重置宏配置

### 数据格式

宏数据编码格式:
- `0x01 + keyCode`: 按键按下
- `0x02 + keyCode`: 按键释放
- `0x03 + interval`: 时间延时

## 组件结构

```
MacroRecording/
├── MacroRecording.jsx      # 主组件
├── MacroContext.jsx        # 宏状态管理
├── useMacroRecording.js    # 录制钩子
├── TimingSettings.jsx      # 时间设置组件
├── MacroProtocol.js        # 协议通信
├── GetMacro.js            # 获取宏配置
├── SetMacro.js            # 设置宏配置
├── MacroRecording.css     # 样式文件
└── index.js               # 模块导出
```

## 使用方法

### 基本使用

```jsx
import { MacroRecording, MacroProvider } from './components/MacroRecording';

function App() {
  return (
    <MacroProvider>
      <MacroRecording />
    </MacroProvider>
  );
}
```

### 自定义钩子

```jsx
import { useMacroRecording } from './components/MacroRecording';

function CustomRecorder() {
  const {
    isRecording,
    recordedEvents,
    startRecording,
    stopRecording,
    getRecordingStats
  } = useMacroRecording();
  
  // 使用录制功能
}
```

## 多语言支持

支持以下语言:
- 简体中文 (zh-CN)
- 繁体中文 (zh-TW)  
- English (en-US)
- 日本語 (ja-JP)
- 한국어 (ko-KR)

## 技术实现

### 事件捕获
使用DOM事件监听器捕获键盘事件，支持:
- `keydown` 事件捕获
- `keyup` 事件捕获
- 事件时间戳记录
- 修饰键状态记录

### 状态管理
- React Context 管理全局宏状态
- 自定义钩子封装录制逻辑
- 本地状态与设备状态同步

### 协议通信
- HID设备通信
- 数据队列管理
- 错误处理和重试机制

## 注意事项

1. **浏览器兼容性**: 需要支持WebHID API的现代浏览器
2. **权限要求**: 需要用户授权HID设备访问权限
3. **事件过滤**: 自动过滤系统按键和修饰键组合
4. **缓存限制**: 受设备宏缓存大小限制
5. **录制质量**: 建议在录制时避免其他应用干扰

## 故障排除

### 常见问题

1. **录制无响应**
   - 检查设备连接状态
   - 确认浏览器权限设置
   - 重新连接设备

2. **宏无法保存**
   - 检查设备缓存空间
   - 验证宏数据格式
   - 确认设备协议支持

3. **时间间隔不准确**
   - 选择合适的时间模式
   - 调整自定义间隔设置
   - 考虑系统性能影响
