// 宏键协议通信工具函数

import { changeToHex } from '../../utils/hidUtils';

export class MacroProtocol {
  constructor(addToQueue) {
    this.addToQueue = addToQueue;
  }

  // 获取键盘总的最大可设置macro数目
  getMaxMacroCount() {
    this.addToQueue("0C");
  }

  // 获取键盘macro缓存大小
  getMacroBufferSize() {
    this.addToQueue("0D");
  }

  // 获取键盘macro配置
  getMacroConfig(offset = 0, length = 28) {
    const offsetHigh = changeToHex((offset >> 8) & 0xFF);
    const offsetLow = changeToHex(offset & 0xFF);
    const lengthHex = changeToHex(length);
    this.addToQueue(`0E ${offsetHigh} ${offsetLow} ${lengthHex}`);
  }

  // 设置键盘macro配置数据
  setMacroConfig(offset, data) {
    const offsetHigh = changeToHex((offset >> 8) & 0xFF);
    const offsetLow = changeToHex(offset & 0xFF);
    const length = changeToHex(data.length);
    const dataHex = data.map(byte => changeToHex(byte)).join(' ');
    this.addToQueue(`0F ${offsetHigh} ${offsetLow} ${length} ${dataHex}`);
  }

  // 重置键盘macro配置
  resetMacroConfig() {
    this.addToQueue("10");
  }

  // 设置M1宏的示例数据（根据用户提供的示例）
  setM1MacroExample() {
    // 设置M1的示例参考数据
    this.addToQueue("0D 02 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00");
    this.addToQueue("10 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00");
    this.addToQueue("0F 01 FF 01 FF 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00");
    this.addToQueue("0F 00 00 1C 01 02 07 01 04 37 35 7C 01 03 07 01 04 31 33 34 7C 01 02 07 01 04 31 31 31 7C 01 03");
    this.addToQueue("0F 00 1C 1C 07 01 04 33 31 36 7C 01 02 16 01 04 31 30 36 7C 01 03 16 01 04 31 33 35 7C 01 02 16");
    this.addToQueue("0F 00 38 18 01 04 37 35 7C 01 03 16 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00");
  }

  // 编码宏数据
  encodeMacroData(macroEvents, timingMode = 'actual', customInterval = 50) {
    const data = [];
    
    for (let i = 0; i < macroEvents.length; i++) {
      const event = macroEvents[i];
      
      // 事件类型：01=按下，02=释放，03=延时
      if (event.type === 'keydown') {
        data.push(0x01);
        data.push(event.keyCode);
      } else if (event.type === 'keyup') {
        data.push(0x02);
        data.push(event.keyCode);
      }
      
      // 添加时间间隔
      if (i < macroEvents.length - 1) {
        let interval;
        if (timingMode === 'actual') {
          interval = macroEvents[i + 1].timestamp - event.timestamp;
        } else if (timingMode === 'standard') {
          interval = 50; // 标准50ms间隔
        } else {
          interval = customInterval;
        }
        
        // 时间间隔编码（简化处理）
        data.push(0x03);
        data.push(Math.min(interval, 255));
      }
    }
    
    return data;
  }

  // 解码宏数据
  decodeMacroData(data) {
    const events = [];
    let timestamp = 0;
    
    for (let i = 0; i < data.length; i += 2) {
      const eventType = data[i];
      const value = data[i + 1];
      
      if (eventType === 0x01) {
        events.push({
          type: 'keydown',
          keyCode: value,
          timestamp
        });
      } else if (eventType === 0x02) {
        events.push({
          type: 'keyup',
          keyCode: value,
          timestamp
        });
      } else if (eventType === 0x03) {
        timestamp += value;
      }
    }
    
    return events;
  }
}

export default MacroProtocol;
