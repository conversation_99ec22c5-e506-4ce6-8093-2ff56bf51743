import React, { createContext, useContext, useState, useEffect } from 'react';
import { useHandleDevice } from '../HIDDevice/HandleDeviceContext';
import MacroProtocol from './MacroProtocol';

const MacroContext = createContext({
  macros: [],
  macroConfig: {
    maxMacroCount: 0,
    bufferSize: 0,
    usedBuffer: 0,
    configData: {}
  },
  addMacro: () => {},
  updateMacro: () => {},
  deleteMacro: () => {},
  getMacroById: () => {},
  syncWithDevice: () => {},
  resetAllMacros: () => {}
});

export const MacroProvider = ({ children }) => {
  const { addToQueue } = useHandleDevice();
  const [macros, setMacros] = useState([]);
  const [macroConfig, setMacroConfig] = useState({
    maxMacroCount: 0,
    bufferSize: 0,
    usedBuffer: 0,
    configData: {}
  });

  const macroProtocol = new MacroProtocol(addToQueue);

  // 初始化宏配置
  useEffect(() => {
    syncWithDevice();
  }, []);

  // 与设备同步
  const syncWithDevice = () => {
    macroProtocol.getMaxMacroCount();
    macroProtocol.getMacroBufferSize();
    macroProtocol.getMacroConfig();
  };

  // 添加宏
  const addMacro = (macroData) => {
    const newMacro = {
      id: Date.now().toString(),
      name: macroData.name || `Macro ${macros.length + 1}`,
      events: macroData.events || [],
      timingMode: macroData.timingMode || 'actual',
      customInterval: macroData.customInterval || 50,
      assignedKey: macroData.assignedKey || null,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...macroData
    };

    setMacros(prev => [...prev, newMacro]);
    
    // 发送到设备
    const protocolData = macroProtocol.encodeMacroData(
      newMacro.events, 
      newMacro.timingMode, 
      newMacro.customInterval
    );
    
    // 计算偏移量（简化处理，实际应该根据已有宏的大小计算）
    const offset = macros.length * 64; // 假设每个宏占用64字节
    macroProtocol.setMacroConfig(offset, protocolData);
    
    return newMacro;
  };

  // 更新宏
  const updateMacro = (macroId, updates) => {
    setMacros(prev => prev.map(macro => {
      if (macro.id === macroId) {
        const updatedMacro = {
          ...macro,
          ...updates,
          updatedAt: new Date()
        };
        
        // 如果更新了事件或时间设置，重新发送到设备
        if (updates.events || updates.timingMode || updates.customInterval) {
          const protocolData = macroProtocol.encodeMacroData(
            updatedMacro.events,
            updatedMacro.timingMode,
            updatedMacro.customInterval
          );
          
          const macroIndex = prev.findIndex(m => m.id === macroId);
          const offset = macroIndex * 64;
          macroProtocol.setMacroConfig(offset, protocolData);
        }
        
        return updatedMacro;
      }
      return macro;
    }));
  };

  // 删除宏
  const deleteMacro = (macroId) => {
    setMacros(prev => {
      const newMacros = prev.filter(macro => macro.id !== macroId);
      
      // 重新排列设备中的宏数据
      newMacros.forEach((macro, index) => {
        const protocolData = macroProtocol.encodeMacroData(
          macro.events,
          macro.timingMode,
          macro.customInterval
        );
        const offset = index * 64;
        macroProtocol.setMacroConfig(offset, protocolData);
      });
      
      return newMacros;
    });
  };

  // 根据ID获取宏
  const getMacroById = (macroId) => {
    return macros.find(macro => macro.id === macroId);
  };

  // 重置所有宏
  const resetAllMacros = () => {
    setMacros([]);
    macroProtocol.resetMacroConfig();
  };

  // 计算已使用的缓存大小
  const calculateUsedBuffer = () => {
    let usedSize = 0;
    macros.forEach(macro => {
      // 简化计算：每个事件2字节，加上头部信息
      usedSize += macro.events.length * 2 + 8;
    });
    return usedSize;
  };

  // 更新缓存使用情况
  useEffect(() => {
    const usedBuffer = calculateUsedBuffer();
    setMacroConfig(prev => ({
      ...prev,
      usedBuffer
    }));
  }, [macros]);

  // 检查是否可以添加新宏
  const canAddMacro = () => {
    return macros.length < macroConfig.maxMacroCount;
  };

  // 检查缓存是否足够
  const hasEnoughBuffer = (estimatedSize) => {
    return macroConfig.usedBuffer + estimatedSize <= macroConfig.bufferSize;
  };

  // 获取宏统计信息
  const getMacroStats = () => {
    const totalEvents = macros.reduce((sum, macro) => sum + macro.events.length, 0);
    const assignedMacros = macros.filter(macro => macro.assignedKey).length;
    
    return {
      totalMacros: macros.length,
      maxMacros: macroConfig.maxMacroCount,
      totalEvents,
      assignedMacros,
      bufferUsage: macroConfig.bufferSize > 0 
        ? (macroConfig.usedBuffer / macroConfig.bufferSize * 100).toFixed(1)
        : 0
    };
  };

  // 导出宏配置
  const exportMacros = () => {
    const exportData = {
      version: '1.0',
      exportDate: new Date().toISOString(),
      macros: macros.map(macro => ({
        ...macro,
        id: undefined // 移除ID，导入时重新生成
      }))
    };
    
    return JSON.stringify(exportData, null, 2);
  };

  // 导入宏配置
  const importMacros = (jsonData) => {
    try {
      const importData = JSON.parse(jsonData);
      
      if (!importData.macros || !Array.isArray(importData.macros)) {
        throw new Error('Invalid macro data format');
      }
      
      // 清空现有宏
      resetAllMacros();
      
      // 导入新宏
      importData.macros.forEach(macroData => {
        addMacro(macroData);
      });
      
      return { success: true, count: importData.macros.length };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const contextValue = {
    macros,
    macroConfig,
    setMacroConfig,
    addMacro,
    updateMacro,
    deleteMacro,
    getMacroById,
    syncWithDevice,
    resetAllMacros,
    canAddMacro,
    hasEnoughBuffer,
    getMacroStats,
    exportMacros,
    importMacros
  };

  return (
    <MacroContext.Provider value={contextValue}>
      {children}
    </MacroContext.Provider>
  );
};

export const useMacro = () => {
  const context = useContext(MacroContext);
  if (!context) {
    throw new Error('useMacro must be used within a MacroProvider');
  }
  return context;
};

export default MacroContext;
