.macro-recording-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.recording-controls {
  margin-bottom: 20px;
  padding: 16px;
  background: #1a1a1a;
  border-radius: 8px;
  border: 1px solid #333;
}

.timing-settings {
  margin-bottom: 20px;
}

.timing-settings .ant-space {
  align-items: center;
}

.macro-list {
  margin-top: 20px;
}

.macro-list .ant-list-item {
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 8px;
  margin-bottom: 8px;
  padding: 16px;
}

.macro-list .ant-list-item:hover {
  background: #252525;
  border-color: #555;
}

.macro-list .ant-list-item-meta-title {
  color: #fff;
  font-weight: 500;
}

.macro-list .ant-list-item-meta-description {
  color: #888;
}

.macro-list .ant-list-item-action {
  margin-left: 8px;
}

.recording-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.recording-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ff4d4f;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.macro-events-preview {
  max-height: 200px;
  overflow-y: auto;
  background: #0f0f0f;
  border: 1px solid #333;
  border-radius: 4px;
  padding: 8px;
  margin-top: 8px;
}

.macro-event-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  border-bottom: 1px solid #222;
  font-family: monospace;
  font-size: 12px;
}

.macro-event-item:last-child {
  border-bottom: none;
}

.macro-event-type {
  color: #52c41a;
}

.macro-event-type.keyup {
  color: #ff7875;
}

.macro-event-key {
  color: #1890ff;
  font-weight: bold;
}

.macro-event-timestamp {
  color: #888;
  font-size: 11px;
}

.macro-stats {
  display: flex;
  gap: 16px;
  margin-top: 16px;
  padding: 12px;
  background: #0f0f0f;
  border-radius: 6px;
  border: 1px solid #333;
}

.macro-stat-item {
  text-align: center;
}

.macro-stat-value {
  display: block;
  font-size: 18px;
  font-weight: bold;
  color: #1890ff;
}

.macro-stat-label {
  display: block;
  font-size: 12px;
  color: #888;
  margin-top: 4px;
}

.timing-mode-description {
  margin-top: 8px;
  padding: 8px;
  background: #0f0f0f;
  border-radius: 4px;
  border: 1px solid #333;
  font-size: 12px;
  color: #888;
}

.macro-buffer-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #0f0f0f;
  border-radius: 4px;
  border: 1px solid #333;
  margin-bottom: 16px;
}

.buffer-usage {
  color: #1890ff;
  font-weight: 500;
}

.buffer-warning {
  color: #ff7875;
}

.macro-assignment {
  margin-top: 16px;
  padding: 12px;
  background: #1a1a1a;
  border-radius: 6px;
  border: 1px solid #333;
}

.macro-assignment-title {
  margin-bottom: 8px;
  color: #fff;
  font-weight: 500;
}

.key-selector {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(40px, 1fr));
  gap: 4px;
  max-width: 400px;
}

.key-selector-item {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #333;
  border: 1px solid #555;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  color: #fff;
  transition: all 0.2s;
}

.key-selector-item:hover {
  background: #555;
  border-color: #777;
}

.key-selector-item.selected {
  background: #1890ff;
  border-color: #1890ff;
  color: #fff;
}

.key-selector-item.assigned {
  background: #52c41a;
  border-color: #52c41a;
}
