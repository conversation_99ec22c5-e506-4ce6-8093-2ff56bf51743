import InitKeyboardMapping from './InitKeyboardMapping';
import SetKeymap from '../Keymap/SetKetmap';
import SetLight from '../Light/SetLight';
import SetPerformance from '../Performance/SetPerformance';
import SetAdvancedKey from '../AdvancedKey/SetAdvancedKey';
import SetVersion from '../Settings/SetVersion';
import SetProfile from '../Profile/SetProfile';
import SetStableMode from '../StableMode/SetStableMode';
import SetSn from '../Settings/SetSn';
import SetKeyVoltage from '../KeyTest/SetKeyVoltage';
import SetKeyClickCount from '../KeyTest/SetKeyClickCount';
import SetFullKeyNoClick from '../Settings/GetFullKeyNoClick/SetFullKeyNoClick';
import SetMacro from '../MacroRecording/SetMacro';
import { parseHex, changeToHex } from '../../utils/hidUtils';

const InitAllInfo = ({data, callbacks}) => {
  const { setCurrentLayer, updateKeycap, setBacklight } = callbacks;
  const { setPerformance, performance } = callbacks;
  const { setAdvancedKey, advancedKey } = callbacks;
  const { addToQueue } = callbacks;
  const { setFirmwareVersion, pid } = callbacks;
  const { setProfile } = callbacks;
  const { setStableMode, stableMode } = callbacks;
  const { setSn } = callbacks;
  const { setContentLoading } = callbacks;
  const { setCalibrationKeys } = callbacks;
  const { setFullKeyNoClick } = callbacks;
  const { setMacroConfig } = callbacks;
  var hexArray = data.split(' ');
  if (hexArray[0] === '05') {
    SetKeymap(hexArray, setCurrentLayer, updateKeycap);
  } else if (hexArray[0] === '12') {
    InitKeyboardMapping(hexArray, setCurrentLayer, updateKeycap, pid);
  } else if (hexArray[0] === '08') {
    if (hexArray[1] === '00' && hexArray[2] === '0B') {
      SetFullKeyNoClick(hexArray, setFullKeyNoClick);
    } else {
      SetLight(hexArray, setBacklight, updateKeycap, pid);
    }
  } else if (hexArray[0] === '36') {
    SetLight(hexArray, setBacklight, updateKeycap, pid);
  } else if (hexArray[0] === '37') {
    SetLight(hexArray, setBacklight, updateKeycap, pid);
  } else if (hexArray[0] === '1A') {
    SetPerformance(hexArray, setCurrentLayer, updateKeycap, setPerformance, performance, stableMode);
  } else if (hexArray[0] === '19') {
    SetPerformance(hexArray, setCurrentLayer, updateKeycap, setPerformance, performance, stableMode);
  } else if (hexArray[0] === '2E') {
    for (let i = 0; i < parseHex(hexArray[1]); i++) {
      addToQueue(`30 ${changeToHex(i)}`)
    }
  } else if (hexArray[0] === '1E') {
    for (let i = 0; i < parseHex(hexArray[1]); i++) {
      addToQueue(`20 ${changeToHex(i)}`)
    }
  } else if (hexArray[0] === '22') {
    for (let i = 0; i < parseHex(hexArray[1]); i++) {
      addToQueue(`24 ${changeToHex(i)}`)
    }
  } else if (hexArray[0] === '2A') {
    for (let i = 0; i < parseHex(hexArray[1]); i++) {
      addToQueue(`2C ${changeToHex(i)}`)
    }
  } else if (hexArray[0] === '26') {
    for (let i = 0; i < parseHex(hexArray[1]); i++) {
      addToQueue(`28 ${changeToHex(i)}`)
    }
  } else if (hexArray[0] === '24') {
    SetAdvancedKey(hexArray, setCurrentLayer, updateKeycap, setAdvancedKey, advancedKey);
  } else if (hexArray[0] === '30') {
    SetAdvancedKey(hexArray, setCurrentLayer, updateKeycap, setAdvancedKey, advancedKey);
  } else if (hexArray[0] === '20') {
    SetAdvancedKey(hexArray, setCurrentLayer, updateKeycap, setAdvancedKey, advancedKey);
  } else if (hexArray[0] === '2C') {
    SetAdvancedKey(hexArray, setCurrentLayer, updateKeycap, setAdvancedKey, advancedKey);
  } else if (hexArray[0] === '28') {
    SetAdvancedKey(hexArray, setCurrentLayer, updateKeycap, setAdvancedKey, advancedKey);
  } else if (hexArray[0] === 'F5') {
    SetVersion(hexArray, setFirmwareVersion);
  } else if (hexArray[0] === '45') {
    SetStableMode(hexArray, setStableMode, stableMode);
  } else if (hexArray[0] === '46') {
    SetStableMode(hexArray, setStableMode, stableMode);
  } else if (hexArray[0] === '39') {
    SetProfile(hexArray, setProfile, setContentLoading);
  } else if (hexArray[0] === '4B') {
    SetSn(hexArray, setSn);
  } else if (hexArray[0] === '4D') {
    SetKeyVoltage(hexArray, updateKeycap, pid);
  } else if (hexArray[0] === '4E') {
    SetKeyVoltage(hexArray, updateKeycap, pid);
  } else if (hexArray[0] === '1B') {
    SetKeyVoltage(hexArray, updateKeycap, pid);
  } else if (hexArray[0] === '50') {
    SetKeyClickCount(hexArray, setCalibrationKeys);
  } else if (hexArray[0] === '0C' || hexArray[0] === '0D' || hexArray[0] === '0E') {
    // 处理宏键协议响应
    if (setMacroConfig) {
      SetMacro(hexArray, setMacroConfig);
    }
  }
};

export default InitAllInfo;
