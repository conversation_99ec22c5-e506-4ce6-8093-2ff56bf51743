import React, { useState, useEffect, createContext, useContext } from 'react';

import { KeyboardContext } from '../Keyboard/KeyboardContext';
import GetAllInfo from './GetAllInfo';
import InitAllInfo from './InitAllInfo';
import { initialBacklightState } from './BacklightContext';
import { initialPerformanceState } from './PerformanceContext';
import { initialAdvancedKeyState } from './AdvancedKeyContext';
import { initialProfileState } from './ProfileContext';
export const HandleDeviceContext = createContext({
  device: null,
  deviceStatus: 'no-device',
  deviceName: null,
  deviceProductId: null,
  firmwareVersion: null,
  sn: null,
  fullKeyNoClick: false,
  setFullKeyNoClick: () => {},
  keyboardLoading: false,
  fullScreenLoading: false,
  fullScreenLoadingText: "配置中...",
  fullScreenPercent: 0,
  showEnterDriver: false,
  setShowEnterDriver: () => {},
  updatingFirmware: false,
  showAdvancedKeyTooltip: false,
  contentLoading: false,
  stableMode: false,
  setStableMode: () => {},
  updateModalVisible: false,
  setUpdateModalVisible: () => {},
  forceUpdate: false,
  setForceUpdate: () => {},
  downloadUrl: '',
  setDownloadUrl: () => {},
  latestVersion: null,
  updateLog: '',
  setLatestVersion: () => {},
  adjustTimes: 0,
  setAdjustTimes: () => {},
  needAdjustKeys: [],
  setNeedAdjustKeys: () => {},
  adjustStatus: '',
  setAdjustStatus: () => {},
  setFullScreenLoadingText: () => {},
  setKeyboardLoading: () => {},
  setContentLoading: () => {},
  setFullScreenLoading: () => {},
  setFullScreenPercent: () => {},
  setUpdatingFirmware: () => {},
  setFirmwareVersion: () => {},
  setSn: () => {},
  setDevice: () => {},
  setDeviceStatus: () => {},
  setDeviceName: () => {},
  setDeviceProductId: () => {},
  handleOpenDevice: () => {},
  addToQueue: () => {},
  dataQueue: () => {},
  ...initialBacklightState,
  ...initialPerformanceState,
  ...initialAdvancedKeyState,
  ...initialProfileState
});

class DataQueue {
  constructor() {
    this.queue = [];
    this.isProcessing = false;
    this.send_data = null;
  }

  setSendData(sendDataFn) {
    this.send_data = sendDataFn;
  }

  addToQueue(data) {
    let cleanString = data.replace(/\s+/g, '');

    // 计算现有字符串的字节数
    const currentLength = cleanString.length / 2; // 每两个字符是一个字节

    // 计算需要补充的字节数
    const neededLength = 32 - currentLength;

    // 如果需要补充字节，填充 '00' 到剩余长度
    if (neededLength > 0) {
        cleanString += '00'.repeat(neededLength); // 补充 '00'
    }

    this.queue.push(cleanString.match(/.{2}/g).join(' '));
    this.processQueue();
  }

  async processQueue() {
    if (this.isProcessing) return;
    this.isProcessing = true;

    if (this.queue.length > 0 && this.send_data) {
      const data = this.queue.shift();
      await this.send_data(data);
    }
  }

  continueProcessing() {
    if (this.queue.length > 0) {
      this.processQueue();
    } else {
      this.isProcessing = false;
    }
  }

  reset() {
    this.queue = [];
    this.isProcessing = false;
  }
}

export function HandleDeviceProvider({ children }) {
  const [device, setDevice] = useState(null);
  const [deviceStatus, setDeviceStatus] = useState('no-device');
  const [deviceName, setDeviceName] = useState(null);
  const [deviceProductId, setDeviceProductId] = useState(null);
  const [backlight, setBacklight] = useState(initialBacklightState.backlight);
  const [performance, setPerformance] = useState(initialPerformanceState.performance);
  const [advancedKey, setAdvancedKey] = useState(initialAdvancedKeyState.advancedKey);
  const [profile, setProfile] = useState(initialProfileState.profile);
  const [firmwareVersion, setFirmwareVersion] = useState(null);
  const [sn, setSn] = useState(null);
  const [stableMode, setStableMode] = useState(false);
  const [keyboardLoading, setKeyboardLoading] = useState(false);
  const [fullScreenLoading, setFullScreenLoading] = useState(false);
  const [fullScreenLoadingText, setFullScreenLoadingText] = useState("配置中...");
  const [fullScreenPercent, setFullScreenPercent] = useState(0);
  const [contentLoading, setContentLoading] = useState(false);
  const [showAdvancedKeyTooltip, setShowAdvancedKeyTooltip] = useState(false);
  const [updatingFirmware, setUpdatingFirmware] = useState(false);
  const [newVersion, setNewVersion] = useState(null);
  const [updateModalVisible, setUpdateModalVisible] = useState(false);
  const [forceUpdate, setForceUpdate] = useState(false);
  const [downloadUrl, setDownloadUrl] = useState('');
  const [latestVersion, setLatestVersion] = useState(null);
  const [updateLog, setUpdateLog] = useState('');
  const [dataQueue] = useState(() => new DataQueue());
  const [fullKeyNoClick, setFullKeyNoClick] = useState(false);
  const [adjustTimes, setAdjustTimes] = useState(0);
  const [needAdjustKeys, setNeedAdjustKeys] = useState([]);
  const [adjustStatus, setAdjustStatus] = useState('');
  const [showEnterDriver, setShowEnterDriver] = useState(false);
  const [macroConfig, setMacroConfig] = useState({
    maxMacroCount: 0,
    bufferSize: 0,
    configData: {}
  });
  const { setCurrentLayer, updateKeycap, setCalibrationKeys } = useContext(KeyboardContext);

  const send_data = async (iptOutput) => {
    try {
      if (!device?.opened) {
        throw "Device not opened";
      }
      let outputData;
      let outputDatastr = iptOutput.replace(/\s+/g, "");

      if (outputDatastr.length % 2 === 0 && /^[0-9a-fA-F]+$/.test(outputDatastr)) {
        const byteLength = outputDatastr.length / 2;
        outputData = new Uint8Array(byteLength);
        for (let i = 0; i < byteLength; i++) {
          outputData[i] = parseInt(outputDatastr.substr(i * 2, 2), 16);
        }
      } else {
        throw "Data is not even or 0-9、a-f、A-F";
      }
      await device.sendReport(0, outputData);
    } catch (error) {
      console.log(error);
      dataQueue.reset(); // Reset queue on error
    }
  };

  useEffect(() => {
    dataQueue.setSendData(send_data);
    const initializeDevice = async () => {
      try {
        const devices = await navigator.hid.getDevices();

        if (devices.length === 0) {
          console.log("No device paired");
          return;
        }

        devices.forEach((tmp_device) => {
          if (tmp_device.collections[0].outputReports && tmp_device.collections[0].outputReports.length > 0) {
            setDevice(tmp_device);
          }
        });

        if (device && !device.opened) {
          try {
            await device.open();
            setDeviceStatus('connected-device');
            setDeviceName(`${device.productName}`);
            device_oninputreport(device);
          } catch (error) {
            if (error.name === 'InvalidStateError') {
              console.log('Device state change already in progress, skipping...');
            } else {
              console.error(error);
            }
          }
        }
      } catch (error) {
        console.log(error);
      }
    };

    initializeDevice();

    navigator.hid.onconnect = (event) => {
      console.log("HID connected: ", event.device);
      if (event.device.collections[0].outputReports && event.device.collections[0].outputReports.length > 0) {
        dataQueue.reset(); // Reset queue on new connection
        setDevice(event.device);
        event.device.open().then(() => {
          setDeviceStatus('connected-device');
          setDeviceName(`${event.device.productName}`);
          device_oninputreport(event.device);
        });
      }
    };

    navigator.hid.ondisconnect = (event) => {
      if (event.device.collections[0].outputReports && event.device.collections[0].outputReports.length > 0) {
        dataQueue.reset(); // Reset queue on disconnect
        setDevice(null);
        setDeviceStatus('no-device');
        setDeviceName(null);
        setDeviceProductId(null);
        setShowEnterDriver(false);
      }
    };
  }, [device]);

  const handleOpenDevice = async () => {
    try {
      const devices = await navigator.hid.requestDevice({
        filters: [
          // EZ80 自研新固件
          { usagePage: 0xff60, usage: 0x61, vendorId: 13495, productId: 9010 },
          { // EZ80 自研新固件更新状态
            usagePage: 0xff60, usage: 0x61, vendorId: 13495, productId: 4660 },
          { // EZ75 自研新固件
            usagePage: 0xff60, usage: 0x61, vendorId: 14441, productId: 29952 },
          { // EZ75 自研新固件更新状态
            usagePage: 0xff60, usage: 0x61, vendorId: 14441, productId: 29953 },
          { // 新EZ60
            usagePage: 0xff60, usage: 0x61, vendorId: 51966, productId: 36880 },
          { // 新EZ63 新固件
            usagePage: 0xff60, usage: 0x61, vendorId: 51966, productId: 36869 },
          { // EZ63 老固件
            usagePage: 0xff60, usage: 0x61, vendorId: 51966, productId: 32773 },
          { // EZ63 自研新固件
            usagePage: 0xff60, usage: 0x61, vendorId: 99, productId: 25344 },
          { // EZ63 自研新固件更新状态
            usagePage: 0xff60, usage: 0x61, vendorId: 99, productId: 25345 },
          { // EZ60
            usagePage: 0xff60, usage: 0x61, vendorId: 51966, productId: 32784 }
        ]
      });

      if (devices.length === 0) {
        console.log("No device selected");
        return;
      }

      const selectedDevice = devices[0];
      dataQueue.reset(); // Reset queue before setting new device
      setDevice(selectedDevice);

      if (!selectedDevice.opened) {
        await selectedDevice.open();
        setDeviceStatus('connected-device');
        setDeviceName(`${selectedDevice.productName}`);
        const waitForDeviceOpen = () => {
          if (selectedDevice.opened) {
            device_oninputreport(selectedDevice);
          } else {
            setTimeout(waitForDeviceOpen, 100);
          }
        };
        waitForDeviceOpen();
      }
    } catch (error) {
      console.log(error);
    }
  };

  const device_oninputreport = (selectedDevice) => {
    console.log(selectedDevice);
    if (selectedDevice) {
      setDeviceProductId(selectedDevice.productId);
      selectedDevice.oninputreport = (event) => {
        let array = new Uint8Array(event.data.buffer);
        let hexstr = "";
        for (const data of array) {
          hexstr += (Array(2).join(0) + data.toString(16).toUpperCase()).slice(-2) + " ";
        }
        let pid = selectedDevice.productId;
        const initConfig = {
          data: hexstr,
          callbacks: {
            setDeviceProductId,
            deviceProductId,
            setCurrentLayer,
            updateKeycap,
            setBacklight,
            setPerformance,
            performance,
            setAdvancedKey,
            advancedKey,
            setFirmwareVersion,
            firmwareVersion,
            setSn,
            sn,
            setProfile,
            profile,
            setStableMode,
            stableMode,
            setContentLoading,
            addToQueue: (data) => dataQueue.addToQueue(data),
            pid,
            setCalibrationKeys,
            setFullKeyNoClick,
            fullKeyNoClick,
            setMacroConfig,
            macroConfig
          }
        };

        InitAllInfo(initConfig);

        if (dataQueue.isProcessing) {
          dataQueue.isProcessing = false;
          dataQueue.continueProcessing();
        }
      };
      const waitForDevice = () => {
        if (selectedDevice && selectedDevice.opened && ![4660, 25345, 29953].includes(selectedDevice.productId)) {
          setTimeout(() => {
            GetAllInfo(dataQueue);
          }, 100);
        } else {
          setTimeout(waitForDevice, 100);
        }
      };
      waitForDevice();
    }
  };

  return (
    <HandleDeviceContext.Provider value={{
      device,
      deviceStatus,
      deviceName,
      deviceProductId,
      setDevice,
      setDeviceStatus,
      setDeviceName,
      setDeviceProductId,
      handleOpenDevice,
      addToQueue: (data) => dataQueue.addToQueue(data),
      send_data,
      dataQueue,
      firmwareVersion,
      setFirmwareVersion,
      sn,
      setSn,
      backlight,
      setBacklight,
      performance,
      setPerformance,
      advancedKey,
      setAdvancedKey,
      keyboardLoading,
      setKeyboardLoading,
      fullScreenLoading,
      setFullScreenLoading,
      fullScreenLoadingText,
      setFullScreenLoadingText,
      fullScreenPercent,
      setFullScreenPercent,
      contentLoading,
      setContentLoading,
      stableMode,
      setStableMode,
      profile,
      setProfile,
      showAdvancedKeyTooltip,
      setShowAdvancedKeyTooltip,
      updatingFirmware,
      setUpdatingFirmware,
      newVersion,
      setNewVersion,
      updateModalVisible,
      setUpdateModalVisible,
      forceUpdate,
      setForceUpdate,
      downloadUrl,
      setDownloadUrl,
      latestVersion,
      setLatestVersion,
      updateLog,
      setUpdateLog,
      fullKeyNoClick,
      setFullKeyNoClick,
      adjustTimes,
      setAdjustTimes,
      needAdjustKeys,
      setNeedAdjustKeys,
      adjustStatus,
      setAdjustStatus,
      showEnterDriver,
      setShowEnterDriver,
      macroConfig,
      setMacroConfig,
    }}>
      {children}
    </HandleDeviceContext.Provider>
  );
}

export function useHandleDevice() {
  return useContext(HandleDeviceContext);
}